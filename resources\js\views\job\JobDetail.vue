<template>
	
	<CCard>
		<CCardHeader>
			<ul class="nav">
				<li v-for="(tab, index) in optionTabs" :key="index" class="nav-item">
					<a 
						:class="{ active: isActiveTab(tab.value) }" 
						class="nav-link"
						@click="handleClickTab(tab.value)"
					>
						{{ tab.title }}
					</a>
				</li>
			</ul>
		</CCardHeader>
		<div v-if="state.tabActived === TAB_JOB_DETAIL.DETAIL">
			<CCol :xs="12">
				<BAccordion flush free>
					<WorkflowTransitionsTable
						:flowTransitions="jobDetail?.flow_transitions || []"
						:enableClick="true"
						@show-detail="showDetailModal"
					/>
				</BAccordion>
			</CCol>
			<CRow>
				<CCol :xs="8">
					<BAccordion flush free>
						<BAccordionItem :title="$t('job.general_info')" visible>
							<CForm class="row g-2">
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.name') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.name || $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.job_position') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.job_position?.name || $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.department') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.department?.name || $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.rank') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.assigned_user?.rank?.name || $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.manager') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.manager_users?.length ? jobDetail.manager_users.map(user => user.full_name).join(', ') : $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.followers') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.follower_users?.length ? jobDetail.follower_users.map(user => user.full_name).join(', ') : $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.workflow') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.process_version?.process?.name || $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="6" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.status') }}
									</CFormLabel>
									<div class="fw-normal" v-if="jobDetail?.status">
										<span class="badge rounded-pill bg-info" v-if="jobDetail?.status === SAVE_JOB_STATUS.PENDING">
											<small class="fst-normal text-white">
												{{ $t('option_tab_job.pending') }}
											</small>
										</span>
										<span class="badge rounded-pill bg-warning" v-else-if="jobDetail?.status === SAVE_JOB_STATUS.PROCESSING">
											<small class="fst-normal text-white">
												{{ $t('option_tab_job.processing') }}
											</small>
										</span>
										<span class="badge rounded-pill bg-success" v-else-if="jobDetail?.status === SAVE_JOB_STATUS.COMPLETED">
											<small class="fst-normal text-white">
												{{ $t('option_tab_job.completed') }}
											</small>
										</span>
										<span class="badge rounded-pill bg-danger" v-else-if="jobDetail?.status === SAVE_JOB_STATUS.CANCEL">
											<small class="fst-normal text-white">
												{{ $t('option_tab_job.cancel') }}
											</small>
										</span>
									</div>
									<div v-else>
										{{ $t('common.no_data') }}
									</div>
								</CCol>
								<CCol :md="12" class="mb-3">
									<CFormLabel class="text-secondary">
										{{ $t('job.description') }}
									</CFormLabel>
									<div class="fw-normal">
										{{ jobDetail?.description || $t('common.no_data') }}
									</div>
								</CCol>
							</CForm>
						</BAccordionItem>
						<BAccordionItem :title="$t('job.custom_field')" visible>
							<CForm class="row g-2">
								<JobFieldValues
									:field-values="jobDetail?.job_field_values || []"
									@file-click="handleFileClick"
								/>
							</CForm>
						</BAccordionItem>
					</BAccordion>
				</CCol>
				<CCol :xs="4">
					<BAccordion flush free>
						<BAccordionItem :title="$t('job.history_action')" visible>
							<div v-if="jobDetail?.job_approval_histories && jobDetail.job_approval_histories.length > 0" class="history-timeline">
								<div
									v-for="(history, index) in filteredHistories"
									:key="history.id"
									class="history-item d-flex mb-3"
								>
									<!-- Icon và đường kết nối -->
									<div class="history-icon-wrapper me-3">
										<div class="history-icon">
											<span :class="`material-symbols-outlined text-white p-1 rounded-circle ${getHistoryAction(history).actionIconClass}`">
												{{ getHistoryAction(history).actionIcon }}
											</span>
										</div>
										<!-- Đường kết nối (không hiển thị cho item cuối) -->
										<div
											v-if="index < jobDetail.job_approval_histories.length - 1"
											class="history-line"
										></div>
									</div>

									<!-- Nội dung lịch sử -->
									<div class="history-content flex-grow-1">
										<div class="history-header d-flex justify-content-between align-items-start">
											<div>
												<strong class="history-actor">
													{{ getHistoryActorName(history) }}
												</strong>
												<span class="history-action text-muted ms-2">
													{{ getHistoryAction(history).actionText }}
												</span>
											</div>
											<small class="text-muted history-time">
												{{ formatHistoryDate(history.updated_at) }}
											</small>
										</div>

										<div class="history-stage mt-2">
											<!-- Ngày -->
											<div v-if="history.date" class="mt-1">
												<span class="text-secondary">
													{{ $t('job.history.date') }}: {{ formatDateTime(history.date) }}
												</span>
											</div>
											<!-- Thông tin giai đoạn -->
											<div v-if="history.stage" class="mt-1">
												<span class="text-secondary">
													{{ $t('job.history.stage') }}: {{ history.stage.name }}
												</span>
											</div>
											<!-- Thông tin hành động -->
											<div v-if="history.action" class="mt-1">
												<span class="text-secondary">
													{{ $t('job.history.action') }}: {{ history.action.name }}
												</span>
											</div>
											<!-- Nhận xét -->
											<div v-if="history.comment" class="comment-text mt-1">
												<span class="text-secondary">
													{{ $t('job.history.comment') }}: {{ history.comment }}
												</span>
											</div>
										</div>
									</div>
								</div>
							</div>

							<!-- Trường hợp không có lịch sử -->
							<div v-else class="text-center text-muted py-4">
								<i class="fas fa-history fa-2x mb-2"></i>
								<div>{{ $t('job.history.no_histossry') }}</div>
							</div>
						</BAccordionItem>
					</BAccordion>
				</CCol>
			</CRow>
		</div>
		<div v-if="state.tabActived === TAB_JOB_DETAIL.FILES">
			<BAccordion flush free>
				<BAccordionItem :title="$t('job.files')" visible>
					<CCol :md="12" class="mb-3">
						<div class="fw-normal">
							<template v-if="jobDetail?.files && jobDetail.files.length > 0">
								<div v-for="(file, index) in jobDetail.files" :key="index">
									<a 
										href="#" 
										@click.prevent="handleFileClick(file)"
										class="fw-normal text-decoration-none"
									>
										<p>{{ getFileName(file) }}</p>
									</a>
								</div>
							</template>
							<template v-else>
								{{ $t('common.no_data') }}
							</template>
						</div>
					</CCol>
				</BAccordionItem>
			</BAccordion>
		</div>
	</CCard>

	<!-- Modal xem file -->
	<CModal
		:visible="state.showFileModal"
		@close="state.showFileModal = false"
		size="xl"
		fullscreen
		class="file-preview-modal"
	>
		<CModalHeader>
			<CModalTitle>{{ state.currentFileName }}</CModalTitle>
		</CModalHeader>
		<CModalBody>
			<div v-if="isPreviewableFile(state.currentFile)" class="text-center">
				<img 
					v-if="isImageFile(state.currentFile)" 
					:src="getFileUrl(state.currentFile)" 
					class="img-fluid"
					alt="Preview"
				/>
				<iframe 
					v-else-if="isPdfFile(state.currentFile)"
					:src="getFileUrl(state.currentFile)"
					width="100%"
					height="5000px"
					frameborder="0"
				></iframe>
			</div>
			<div v-else class="text-center">
				<p>{{ $t('job.file_not_previewable') }}</p>
				<CButton color="primary" @click="downloadFile(state.currentFile)">
					{{ $t('job.download') }}
				</CButton>
			</div>
		</CModalBody>
	</CModal>

	<!-- Modal hiển thị chi tiết -->
	<BModal
		:title="state.detailModalTitle"
		v-model="state.showDetailModal"
		centered
		:size="getModalSize"
		scrollable
		hide-footer
		no-close-on-backdrop
        no-close-on-esc
		@close="closeShowDetailModal"
	>
		<StageView
			v-if="['from_stage', 'to_stage', 'back_to_stage'].includes(state.currentDetailType) && state.currentDetailData"
			:stage-type="state.currentDetailType"
			:stage-data="state.currentDetailData"
			:format-approvers="formatApprovers"
			:format-followers="formatFollowers"
		/>

		<div v-else-if="state.currentDetailType === 'action' && state.currentDetailData">
			<CRow>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.action.name')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ state.currentDetailData.action?.name || $t('common.no_data') }}
					</div>
				</CCol>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.action.description')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ state.currentDetailData.action?.description || $t('common.no_data') }}
					</div>
				</CCol>
				<JobFieldValues
					:field-values="state.dataFieldValuesByStage || []"
					@file-click="handleFileClick"
					v-if="!checkCanExecuteTrue"
				/>
			</CRow>
			
			<FormKit
				ref="dynamicForm"
				type="form"
				:actions="false"
				incomplete-message=" "
				@submit="handleSubmitForm"
				v-if="checkCanExecuteTrue"
			>
				<Form ref="form" @submit="handleSubmitForm" :validation-schema="schema(sortedFormFields)">
					<CCardBody>
						<!-- Dynamic Form Fields -->
						<CCol :xs="12" class="mb-3" v-if="state.showDynamicForm">
							<DynamicFormFields
								:form-fields="sortedFormFields"
								:form-data="state.formData"
								:item-childrens="state.itemChildrens"
								:select-option-departments="state.selectOptionDepartments"
								:sub-column-table-description="state.subColumnTableDescription"
								:sub-column-table-option-selected="state.subColumnTableOptionSelected"
								:sub-column-table-description-children="state.subColumnTableDescriptionChildren"
								:sub-column-table-option-selected-children="state.subColumnTableOptionSelectedChildren"
								:formatted-formula-results="formattedFormulaResults"
								:max-files="state.maxFiles"
								:max-file-size="state.maxFileSize"
								:accepted-file-types="state.acceptedFileTypes"
								:get-option-users="getOptionUsersHandler"
								:get-option-column-data="getOptionColumnDataHandler"
								@update-form-data="(key, value) => state.formData[key] = value"
								@update-files="updateFiles"
								@update-file-childrens="updateFileChildrens"
								@add-item="addItem"
								@remove-item="removeItem"
								@show-sub-column-table="showSubColumnTable"
								@show-sub-column-table-children="showSubColumnTableChildren"
							/>
						</CCol>

						<CCol :xs="12" class="mb-3">
							<CFormLabel class="text-secondary">
								{{ $t('workflow.action.comment') }}
							</CFormLabel>
							<CFormTextarea
								id="comment"
								v-model="state.comment"
								:placeholder="$t('workflow.action.comment_desc')"
								:maxlength="500"
								rows="2"
								:invalid="!!state.commentError"
								@input="state.commentError = ''"
							></CFormTextarea>
							<CFormFeedback invalid>
								{{ state.commentError }}
							</CFormFeedback>
						</CCol>
					</CCardBody>
					<CCardFooter>
						<div class="d-flex justify-content-end">
							<CButton
								type="button"
								class="btn btn-light border m-1"
								@click="closeShowDetailModal"
							>
								<span class="text-uppercase">
									{{ $t('workflow.action.close') }}
								</span>
							</CButton>
							<CButton
								type="submit"
								class="btn btn-primary m-1"
								@click="submitForm"
							>
								<span class="text-uppercase">
									{{ $t('workflow.action.execute') }}
								</span>
							</CButton>
						</div>
					</CCardFooter>
				</Form>
			</FormKit>
		</div>
		
		<ConditionView
			v-else-if="state.currentDetailType === 'condition' && state.currentDetailData"
			:dataConditions="state.currentDetailData.condition"
			:fieldCreateds="jobDetail?.process_version?.form?.fields || []"
		/>

		<EmailTemplateView
			v-else-if="state.currentDetailType === 'email_template' && state.currentDetailData"
			:dataEmail="state.currentDetailData.email_template"
			:fieldCreateds="jobDetail?.process_version?.form?.fields || []"
		/>

		<!-- <div v-else-if="state.currentDetailType === 'email_template' && state.currentDetailData">
			<div class="detail-section">
				<div class="detail-header">{{ $t('workflow.email_template_info') }}</div>
				<div class="detail-content">
					<div v-if="state.currentDetailData.email_template && state.currentDetailData.email_template.length">
						<div v-for="(email, eIdx) in state.currentDetailData.email_template" :key="`de-${eIdx}`" class="email-template-item mb-4">
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.template_name') }}:</div>
								<div class="detail-value">{{ email.name || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.subject') }}:</div>
								<div class="detail-value">{{ email.subject || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.from_email') }}:</div>
								<div class="detail-value">{{ email.from_email || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.to_emails') }}:</div>
								<div class="detail-value">{{ Array.isArray(email.to_emails) ? email.to_emails.join(', ') : email.to_emails || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.cc_emails') }}:</div>
								<div class="detail-value">{{ Array.isArray(email.cc_emails) ? email.cc_emails.join(', ') : email.cc_emails || $t('common.no_data') }}</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.bcc_emails') }}:</div>
								<div class="detail-value">{{ Array.isArray(email.bcc_emails) ? email.bcc_emails.join(', ') : email.bcc_emails || $t('common.no_data') }}</div>
							</div>
							<div v-if="email.condition && email.condition.length" class="detail-item">
								<div class="detail-label">{{ $t('workflow.condition') }}:</div>
								<div class="detail-value">
									<div v-for="(condition, ecIdx) in email.condition" :key="`ec-${ecIdx}`" class="mb-1">
										{{ condition.name }} ({{ condition.status ? $t('workflow.enabled') : $t('workflow.disabled') }})
									</div>
								</div>
							</div>
							<div class="detail-item">
								<div class="detail-label">{{ $t('workflow.content') }}:</div>
								<div class="detail-value email-content">
									<div v-html="email.content || '-'"></div>
								</div>
							</div>
						</div>
					</div>
					<div v-else class="no-data">{{ $t('common.no_data') }}</div>
				</div>
			</div>
		</div> -->

		<div v-else-if="state.currentDetailType === 'action_back_to' && state.currentDetailData">
			<CForm class="row g-2">
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.action.name')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ state.currentDetailData.action_back_to?.name || $t('common.no_data') }}
					</div>
				</CCol>
				<CCol :md="6" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.action.description')}}
					</CFormLabel>
					<div class="fw-normal">
						{{ state.currentDetailData.action_back_to?.description || $t('common.no_data') }}
					</div>
				</CCol>
				<CCol :xs="12" class="mb-3">
					<CFormLabel class="text-secondary">
						{{ $t('workflow.action.comment') }}
					</CFormLabel>
					<CFormTextarea
						id="comment"
						v-model="state.comment"
						:placeholder="$t('workflow.action.comment_desc')"
						:maxlength="500" 
						rows="2"
						:invalid="!!state.commentError"
						@input="state.commentError = ''"
					></CFormTextarea>
					<CFormFeedback invalid>
						{{ state.commentError }}
					</CFormFeedback>
				</CCol>
				<CCardFooter>
					<div class="d-flex justify-content-end">
						<CButton 
							type="button"
							class="btn btn-light border m-1"
							@click="closeShowDetailModal"
						>
							<span class="text-uppercase">
								{{ $t('workflow.action.close') }}
							</span>
						</CButton>
						<CButton 
							type="button"
							class="btn btn-primary m-1"
							@click="executeAction(state.currentDetailData, 'action_back_to')"
						>
							<span class="text-uppercase">
								{{ $t('workflow.action.execute') }}
							</span>
						</CButton>
					</div>
				</CCardFooter>
			</CForm>
		</div>

		<div v-else-if="state.currentDetailType === 'email_template_back' && state.currentDetailData">
			<div class="detail-section">
				<div class="detail-header">{{ $t('workflow.back_email_info') }}</div>
				<div class="detail-content">
					<div v-if="state.currentDetailData.email_template_back">
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.template_name') }}:</div>
							<div class="detail-value">{{ state.currentDetailData.email_template_back?.name || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.subject') }}:</div>
							<div class="detail-value">{{ state.currentDetailData.email_template_back?.subject || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.from_email') }}:</div>
							<div class="detail-value">{{ state.currentDetailData.email_template_back?.from_email || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.to_emails') }}:</div>
							<div class="detail-value">{{ Array.isArray(state.currentDetailData.email_template_back?.to_emails) ? state.currentDetailData.email_template_back?.to_emails.join(', ') : state.currentDetailData.email_template_back?.to_emails || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.cc_emails') }}:</div>
							<div class="detail-value">{{ Array.isArray(state.currentDetailData.email_template_back?.cc_emails) ? state.currentDetailData.email_template_back?.cc_emails.join(', ') : state.currentDetailData.email_template_back?.cc_emails || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.bcc_emails') }}:</div>
							<div class="detail-value">{{ Array.isArray(state.currentDetailData.email_template_back?.bcc_emails) ? state.currentDetailData.email_template_back?.bcc_emails.join(', ') : state.currentDetailData.email_template_back?.bcc_emails || $t('common.no_data') }}</div>
						</div>
						<div class="detail-item">
							<div class="detail-label">{{ $t('workflow.content') }}:</div>
							<div class="detail-value email-content">
								<div v-html="state.currentDetailData.email_template_back?.content || '-'"></div>
							</div>
						</div>
					</div>
					<div v-else class="no-data">{{ $t('common.no_data') }}</div>
				</div>
			</div>
		</div>

		<ProcessTransitionView
			v-else-if="state.currentDetailType === 'process_transition' && state.currentDetailData"
			:processTransitionData="state.currentDetailData"
		/>

		<div v-else class="no-detail-data">
			{{ $t('workflow.no_detail_available') }}
		</div>

		<template #footer>
			<CButton color="secondary" @click="closeShowDetailModal">
				{{ $t('common.close') }}
			</CButton>
		</template>
	</BModal>

	<loading :isLoading="setIsLoading" />
</template>

<script lang="ts">
import { defineComponent, reactive, computed, onMounted, onUnmounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import useJobs from '@/composables/job';
import useFiles from '@/composables/files';
import useForms from '@/composables/form';
import useFormValidation from '@/composables/useFormValidation';
import useDynamicFormLogic from '@/composables/useDynamicFormLogic';
import useDynamicFormState from '@/composables/useDynamicFormState';
import useOptionsHandlers from '@/composables/useOptionsHandlers';
import { useI18n } from "vue-i18n";
import { TAB_JOB_DETAIL } from '@/constants/constants';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { languageEventBus } from '@/utils/languageUtils';
import { WORKFLOWS, SAVE_JOB_STATUS } from '@/constants/constants';
import { useToast } from 'vue-toast-notification';
import Loading from '@/views/loading/Loading.vue'
import DynamicFormFields from '@/components/DynamicFormFields.vue'
import JobFieldValues from '@/views/job/JobFieldValues.vue'
import WorkflowTransitionsTable from '@/components/WorkflowTransitionsTable.vue'
import StageView from '@/views/workflow/stage/StageView.vue'
import ProcessTransitionView from '@/views/workflow/process-transition/ProcessTransitionView.vue'
import ConditionView from '@/views/workflow/condition/ConditionView.vue';
import EmailTemplateView from '@/views/workflow/email-template/EmailTemplateView.vue';
import moment from 'moment';

export default defineComponent({
    name: "JobDetail",

	components: {
        Form,
		Field,
		ErrorMessage,
		Loading,
		DynamicFormFields,
		JobFieldValues,
		WorkflowTransitionsTable,
		StageView,
		ProcessTransitionView,
		ConditionView,
		EmailTemplateView
    },
  
    setup() {
        const route = useRoute();
		const { t }  = useI18n();
		const $toast = useToast();

        const {
			setIsLoading,
			showDetailJob,
			jobDetail,
			updateExecuteAction,
			updateExecuteActionBackTo,
			getFieldValuesByStage,
		} = useJobs();

		const {
			getFileName,
			getFileUrl,
			isImageFile,
			isPdfFile,
			isPreviewableFile,
			downloadFile
		} = useFiles();

		const { getFieldsByFormId } = useForms();

		const {
			createDynamicSchema,
			validateRequiredFields
		} = useFormValidation(t);

		const {
			initializeValues: initializeValuesFromComposable,
			initializeItemChildrens,
			addItem: addItemFromComposable,
			removeItem: removeItemFromComposable,
			showSubColumnTable: showSubColumnTableFromComposable,
			showSubColumnTableChildren: showSubColumnTableChildrenFromComposable,
			updateFiles: updateFilesFromComposable,
			updateFileChildrens: updateFileChildrensFromComposable
		} = useDynamicFormLogic();

		// Use shared validation schema
		const schema = (field: any[]) => createDynamicSchema(field);

		const {
			getOptionUsers: getOptionUsersHandler,
			getOptionDepartments: getOptionDepartmentsHandler,
			getOptionColumnData: getOptionColumnDataHandler
		} = useOptionsHandlers();

		// Use shared composables giống JobAdd.vue
		const {
			createDynamicFormState,
			useSortedFormFields,
			useFormulaCalculations
		} = useDynamicFormState();

		// Ref for FormKit
		const dynamicForm = ref();

		// Use shared dynamic form state
		const dynamicFormState = createDynamicFormState();

		// JobDetail-specific state (keep separate)
		const jobDetailState = reactive({
			showFileModal: false,
			currentFile: '',
			currentFileName: '',
			tabActived: TAB_JOB_DETAIL.DETAIL,
			showDetailModal: false,
			currentDetailType: '',
			currentDetailData: null as any,
			detailModalTitle: '',
			comment: '',
			commentError: '',
			showDynamicForm: false,
			dataFieldValuesByStage: [] as Array<any>,
			fieldCreateds: [] as Array<any>,
		});

		// Combine states giống JobAdd.vue
		const state = reactive({
			...dynamicFormState,
			...jobDetailState
		});

		// Sử dụng computed để optionTabs cập nhật khi ngôn ngữ thay đổi
		const optionTabs = computed(() => [
            { value: TAB_JOB_DETAIL.DETAIL, title: t('option_tab_job_detail.detail') },
			{ value: TAB_JOB_DETAIL.FILES, title: t('option_tab_job_detail.files') },
		]);

		// Use shared computed properties giống JobAdd.vue
		const sortedFormFields = useSortedFormFields(state);
		const formulaCalculations = useFormulaCalculations(sortedFormFields, state.formData, state.itemChildrens);
		const formattedFormulaResults = formulaCalculations.formattedFormulaResults;



		const isActiveTab = (valueTabActived: string) => {
			return state.tabActived === valueTabActived;
		};

		const handleClickTab = (valueTabActived: string) => {
			state.tabActived = valueTabActived;
		};

		const handleFileClick = (file: string) => {
			state.currentFile = file;
			state.currentFileName = getFileName(file);
			state.showFileModal = true;
		};

		// Hàm xử lý và hiển thị thông tin người phê duyệt
		const formatApprovers = (approvers: any) => {
			if (!approvers) return t('common.no_data');
			
			return  Array.isArray(approvers) ? approvers.join(', ') : approvers || t('common.no_data');
		};
		
		// Hàm xử lý và hiển thị thông tin người theo dõi
		const formatFollowers = (followers: any): string => {
			if (!followers) return t('common.no_data');

			return  Array.isArray(followers) ? followers.join(', ') : followers || t('common.no_data');
		};

		// Computed property for modal size based on currentDetailType
		const getModalSize = computed(() => {
			switch (state.currentDetailType) {
				case 'from_stage':
					return 'xl';
				case 'to_stage':
					return 'xl';
				case 'back_to_stage':
					return 'xl';
				case 'action':
					return 'xl';
				case 'action_back_to':
					return 'lg';
				case 'email_template':
				case 'email_template_back':
					return 'xl';
				case 'condition':
					return 'lg';
				case 'process_transition':
					return 'xl';
				default:
					return 'md';
			}
		});

		const showDetailModal = async (type: string, data: any) => {
			state.currentDetailType = type;
			state.currentDetailData = data;

			// Reset dynamic form state
			state.showDynamicForm = false;
			state.dataFormFields = [];
			state.formData = {};
			state.itemChildrens = {};
			state.dataFieldValuesByStage = [];

			switch (type) {
				case 'from_stage':
				case 'to_stage':
				case 'back_to_stage':
					state.detailModalTitle = t(`workflow.feature.stage_title`);
					break;
				case 'action':
					state.detailModalTitle = t(`workflow.feature.action_title`);
					const stageId = data.from_stage.id;
					const jobId = jobDetail.value.id;
					const formId = jobDetail.value.process_version.form_id;
					if (checkCanExecuteTrue.value) {
						// Load dynamic form fields for action
						if (formId && stageId) {
							try {
								state.dataFormFields = await getFieldsByFormId(formId, stageId);
								if (state.dataFormFields && state.dataFormFields.length > 0) {
									state.showDynamicForm = true;
									// Initialize form values
									initializeValuesFromComposable(sortedFormFields.value, state.formData);
									// Initialize item childrens for TABLE fields (tạo 1 dòng mặc định)
									initializeItemChildrens(sortedFormFields.value, state.itemChildrens);
									// Load departments if needed
									await loadDepartments();
								}
							} catch (error) {
								console.error('Error loading form fields:', error);
							}
						}
					} else {
						if (stageId === WORKFLOWS.STAGE.START) return;
						let result = await getFieldValuesByStage(jobId, stageId);
						if (result.status === 'success') {
							state.dataFieldValuesByStage = result.job_field_values_by_stage;
						}
					}
					break;
				case 'action_back_to':
					state.detailModalTitle = t(`workflow.feature.action_title`);
					break;
				case 'email_template':
				case 'email_template_back':
					state.detailModalTitle = t(`workflow.feature.email_title`);
					break;
				case 'condition':
					state.detailModalTitle = t(`workflow.feature.condition_title`);
					break;
				case 'process_transition':
					state.detailModalTitle = t(`workflow.feature.process_transition_title`);
					break;
				default:
					break;
			}
			state.showDetailModal = true;
		};

		const closeShowDetailModal = () => {
			state.showDetailModal = false;
			state.currentDetailData = null;
			state.comment = '';
			state.commentError = '';
			// Reset dynamic form state
			state.showDynamicForm = false;
			state.dataFormFields = [];
			state.formData = {};
			state.itemChildrens = {};
			state.dataFieldValuesByStage = [];
		}

		const executeAction = async (data: any, type: string) => {
			if (!data) return;

			let result: any;
			state.commentError = '';
			const validationErrors: string[] = [];
			const stageId = data.from_stage?.id;
			const pendingApprovalId = jobDetail.value.pending_approval.find((item: any) => item.stage_id === stageId)?.id;

			if (data.from_stage?.comment && !state.comment.trim()) {
				state.commentError = t('workflow.action.comment_desc');
				validationErrors.push(state.commentError);
			}

			// Validate dynamic form if it exists
			if (state.showDynamicForm && state.dataFormFields.length > 0 && type === 'action') {
				validationErrors.push(...validateRequiredFields(sortedFormFields.value, state.formData, state.itemChildrens));
			}

			if (validationErrors.length > 0) {
				const combinedMessage = validationErrors.join('<br>');
				$toast.open({
					message: combinedMessage,
					type: "warning",
					duration: 5000,
					dismissible: true,
					position: "bottom-right",
				});
				return;
			}

			if (type === 'action_back_to') {
				const actionBackToId = data.action_back_to?.id;
				const backToStageId = data.back_to_stage?.id;
				const emailConditionIdsBackTo = data.email_template_back?.flatMap((email: any) => email.condition?.map((condition: any) => condition.id) || []) || [];
				
				result = await updateExecuteActionBackTo(
					route.params.id as string, 
					actionBackToId, 
					stageId, 
					backToStageId, 
					state.comment, 
					pendingApprovalId, 
					emailConditionIdsBackTo
				);
			} else {
				const node = dynamicForm.value.node
				const isValidFormKit = node && node.context?.state?.valid;

				if (!isValidFormKit) {
					console.warn('Form chưa valid hoặc node không tồn tại, không thể submit!')
					return;
				}

				// Prepare FormData giống storeJob trong JobAdd.vue
				let config: object = {
					headers: {
						'Content-Type': 'multipart/form-data'
					}
				};

				const keyFileUploads = sortedFormFields.value.filter((field: any) => field.type === 'FILEUPLOAD').map((field: any) => field.name);
				const keyFileUploadChildrens = sortedFormFields.value.filter((field: any) => field.type === 'TABLE').flatMap((field: any) => field.childrens.filter((field_child: any) => field_child.type === 'FILEUPLOAD').map((field_child: any) => field_child.keyword));

				let formJobData = new FormData();

				// Append file uploads
				keyFileUploads.forEach((key: string) => {
					if (state.formData.hasOwnProperty(key)) {
						state.formData[key].forEach((file: any, index: number) => {
							formJobData.append(`key_file_uploads[${key}][${index}]`, file);
						});
					}
				});

				// Append file uploads from table childrens
				Object.keys(state.itemChildrens).forEach((keyItemChildrens: string) => {
					keyFileUploadChildrens.forEach((keyFileUploadChildren: string) => {
						const checkKeyFileUploadChildren = state.itemChildrens[keyItemChildrens].some((item: any) => item.hasOwnProperty(keyFileUploadChildren));
						if (checkKeyFileUploadChildren) {
							state.itemChildrens[keyItemChildrens].forEach((item: any, itemIndex: number) => {
								if (item[keyFileUploadChildren]) {
									item[keyFileUploadChildren].forEach((file: any, fileIndex: number) => {
										formJobData.append(`key_file_upload_childrens[${itemIndex}][${keyFileUploadChildren}][${fileIndex}]`, file);
									});
								}
							});
						}
					});
				});

				// Append table data
				Object.keys(state.itemChildrens).forEach((keyItemChildrens: string) => {
					formJobData.append(`key_tables[${keyItemChildrens}]`, JSON.stringify(state.itemChildrens[keyItemChildrens]));
				});

				// Append form data
				formJobData.append('formData', JSON.stringify(state.formData));
				formJobData.append('comment', state.comment);

				
				const actionId = data.action?.id;
				const conditionIds = data.condition?.map((condition: any) => condition.id) || [];
				const emailConditionIds = data.email_template?.flatMap((email: any) => email.condition?.map((condition: any) => condition.id) || []) || [];

				// Append action-specific data
				formJobData.append('job_id', route.params.id as string);
				formJobData.append('action_id', actionId);
				formJobData.append('stage_id', stageId);
				formJobData.append('pending_approval_id', pendingApprovalId || '');
				formJobData.append('condition_ids', JSON.stringify(conditionIds));
				formJobData.append('email_condition_ids', JSON.stringify(emailConditionIds));

				result = await updateExecuteAction(formJobData, config);
			}

			if (result && result.status === 'success') {
				refreshJobData();
				closeShowDetailModal();
				$toast.open({
					message: t('toast.status.ACTION_SUCCESS'),
					type: "success",
					duration: 5000,
					dismissible: true,
					position: "bottom-right",
				});
			}
		}

		const refreshJobData = async () => {
			await showDetailJob(route.params.id as string);
		};

		// Dynamic form helper functions
		const addItem = (field: any) => {
			addItemFromComposable(field, state.itemChildrens);
		};

		const removeItem = (field: any, itemIndex: number) => {
			removeItemFromComposable(field, itemIndex, state.itemChildrens);
		};

		const showSubColumnTable = (optionSelected: any, keyName: string) => {
			showSubColumnTableFromComposable(optionSelected, keyName, state.subColumnTableDescription, state.subColumnTableOptionSelected);
		};

		const showSubColumnTableChildren = (optionSelected: any, itemIndex: number, keyName: string) => {
			showSubColumnTableChildrenFromComposable(
				optionSelected,
				itemIndex,
				keyName,
				state.subColumnTableDescriptionChildren,
				state.subColumnTableOptionSelectedChildren
			);
		};

		const updateFiles = (fileItemUploads: any, fieldName: string) => {
			updateFilesFromComposable(fileItemUploads, fieldName, state.formData);
		};

		const updateFileChildrens = (fileItemUploads: any, itemChildren: any, fieldChildrenName: string) => {
			updateFileChildrensFromComposable(fileItemUploads, itemChildren, fieldChildrenName);
		};

		// Load departments when needed
		const loadDepartments = async () => {
			try {
				const departments = await getOptionDepartmentsHandler([]);
				if (Array.isArray(departments)) {
					state.selectOptionDepartments = departments;
				} else {
					state.selectOptionDepartments = [];
				}
			} catch (error) {
				console.error('Error loading departments:', error);
				state.selectOptionDepartments = [];
			}
		};

		// Submit functions giống JobAdd.vue
		const submitForm = () => {
			const node = dynamicForm.value.node;
			node.submit();
			handleSubmitForm();
		};

		const handleSubmitForm = async () => {
			if (setIsLoading.value) {
				return;
			}
			// Determine action type based on current detail type
			const actionType = state.currentDetailType === 'action_back_to' ? 'action_back_to' : 'action';
			await executeAction(state.currentDetailData, actionType);
		};

		// Helper methods cho history timeline
		const getHistoryActorName = (history: any): string => {
			if (history.actor && history.actor.full_name) {
				return history.actor.full_name;
			}

			return t('common.unknown');
		};

		const getHistoryAction = (history: any): any => {
			let actionText = '';
			let actionIcon = '';
			let actionIconClass = '';
			if (!!history.action) {
				actionText = `${t('job.history.action_updated')}`;
				actionIcon = 'check';
				actionIconClass = 'bg-success';
			} else {
				if (history.action_id === WORKFLOWS.ACTION.CREATE) {
					actionText = t('job.history.created_new_job');
					actionIcon = 'add';
					actionIconClass = 'bg-primary';
				}
				if (history.action_id === WORKFLOWS.ACTION.BACK_TO) {
					actionText = t('job.history.back_to_stage');
					actionIcon = 'undo';
					actionIconClass = 'bg-warning';
				}
			}

			return {
				actionText,
				actionIcon,
				actionIconClass
			};
		};

		const formatHistoryDate = (dateString: string): string => {
			if (!dateString) return '';

			try {
				const date = new Date(dateString);
				const now = new Date();
				const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

				if (diffInMinutes < 1) {
					return t('time.just_now');
				} else if (diffInMinutes < 60) {
					return t('time.minutes_ago', { minutes: diffInMinutes });
				} else if (diffInMinutes < 1440) { // 24 hours
					const hours = Math.floor(diffInMinutes / 60);
					return t('time.hours_ago', { hours });
				} else {
					// Hiển thị ngày tháng đầy đủ
					return date.toLocaleString('vi-VN', {
						year: 'numeric',
						month: '2-digit',
						day: '2-digit',
						hour: '2-digit',
						minute: '2-digit'
					});
				}
			} catch (error) {
				return dateString;
			}
		};



		// Handle language change
		const handleLanguageChange = (newLang: string) => {
			refreshJobData();
		};

		// Register language change listener
		onMounted(async () => {
			await showDetailJob(route.params.id as string);
			// Add listener for language changes
			languageEventBus.onLanguageChanged(handleLanguageChange);
		});

		// Clean up language change listener
		onUnmounted(() => {
			// Remove the listener to prevent memory leaks
			const index = languageEventBus.listeners.findIndex(listener => listener === handleLanguageChange);
			if (index !== -1) {
				languageEventBus.listeners.splice(index, 1);
			}
		});

		const filteredHistories = computed(() => {
			return jobDetail.value?.job_approval_histories?.filter((history: any) => history.user_id);
		});

		const formatDateTime = (date: string) => {
            return moment(date).format('HH:mm DD/MM/YYYY ');
        };

		const checkCanExecuteTrue = computed(() => {
			return state.currentDetailData.can_execute;
		});

        return {
			setIsLoading,
			TAB_JOB_DETAIL,
			WORKFLOWS,
			SAVE_JOB_STATUS,
            state,
			optionTabs,
			isActiveTab,
			handleClickTab,
			jobDetail,
			handleFileClick,
			getFileName,
			isPreviewableFile,
			isImageFile,
			isPdfFile,
			downloadFile,
			getFileUrl,
			formatApprovers,
			formatFollowers,
			getModalSize,
			showDetailModal,
			closeShowDetailModal,
			executeAction,
			getHistoryActorName,
			getHistoryAction,
			formatHistoryDate,
			filteredHistories,
			formatDateTime,
			checkCanExecuteTrue,
			// Dynamic form
			sortedFormFields,
			formattedFormulaResults,
			schema,
			addItem,
			removeItem,
			showSubColumnTable,
			showSubColumnTableChildren,
			updateFiles,
			updateFileChildrens,
			getOptionUsersHandler,
			getOptionColumnDataHandler,
			loadDepartments,
			submitForm,
			handleSubmitForm,
			dynamicForm,
        }
    }
});
</script>

<style scoped>
.card-footer {
    z-index: 99;
    position: sticky;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: #f8f9fa;
    padding: 10px;
    box-shadow: 0 -1px 5px rgba(0,0,0,0.1);
}
.object-field-item {
	display: inline-block;
	min-width: 200px;
	white-space: normal;
	word-wrap: break-word;
	padding: 0 10px;
}

/* History Timeline Styles */
.history-timeline {
	position: relative;
}

.history-item {
	position: relative;
}

.history-icon-wrapper {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 40px;
}

.history-icon {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	background-color: #fff;
	border: 2px solid #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	z-index: 2;
}

.history-line {
	width: 2px;
	background-color: #e9ecef;
	flex-grow: 1;
	min-height: 20px;
	margin-top: 8px;
}

.history-content {
	padding-left: 8px;
	padding-bottom: 8px;
}

.history-header {
	margin-bottom: 4px;
}

.history-actor {
	color: #495057;
	font-size: 14px;
}

.history-action {
	font-size: 14px;
}

.history-stage {
	font-size: 12px;
}

.history-time {
	font-size: 12px;
	white-space: nowrap;
}

.history-stage {
	background-color: #f8f9fa;
	border-left: 3px solid #007bff;
	padding: 8px 12px;
	border-radius: 4px;
	font-size: 12px;
	color: #495057;
}

.comment-text {
	line-height: 1.4;
}

.object-main-title {
	display: inline-block;
	min-width: 250px;
	white-space: normal;
	word-wrap: break-word;
	padding-right: 15px;
}

.custom-table {
	table-layout: auto;
	width: 100%;
}

.custom-table th, .custom-table td {
	white-space: normal;
	word-wrap: break-word;
	overflow-wrap: break-word;
	padding: 8px;
}

/* Loại bỏ giới hạn độ rộng cho cột OBJECTSYSTEM */
.custom-table th[data-object-system], 
.custom-table td[data-object-system] {
	width: auto;
}

/* Đảm bảo các cột tự động điều chỉnh theo nội dung */
.custom-table th {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}


</style>
<style src="@vueform/multiselect/themes/default.css"></style>