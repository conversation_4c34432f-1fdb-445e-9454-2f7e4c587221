<template>
	<CForm class="row g-2">
		<CCol :md="6" class="mb-3">
			<CFormLabel class="text-secondary">
				{{ $t('workflow.stage.name')}}
			</CFormLabel>
			<div class="fw-normal">
				{{ getStageData?.name || $t('common.no_data') }}
			</div>
		</CCol>
		<CCol :md="6" class="mb-3">
			<CFormLabel class="text-secondary">
				{{ $t('workflow.stage.description')}}
			</CFormLabel>
			<div class="fw-normal">
				{{ getStageData?.description || $t('common.no_data') }}
			</div>
		</CCol>
		<CCol :md="6" class="mb-3">
			<CFormLabel class="text-secondary">
				{{ $t('workflow.stage.approver')}}
			</CFormLabel>
			<div class="fw-normal">
				{{ formatApprovers(getStageData?.approvers) }}
			</div>
		</CCol>
		<CCol :md="6" class="mb-3">
			<CFormLabel class="text-secondary">
				{{ $t('workflow.stage.follower')}}
			</CFormLabel>
			<div class="fw-normal">
				{{ formatFollowers(getStageData?.followers) }}
			</div>
		</CCol>
	</CForm>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { useI18n } from "vue-i18n";

export default defineComponent({
	name: "StageView",
	
	props: {
		stageType: {
			type: String,
			required: true,
			validator: (value: string) => ['from_stage', 'to_stage', 'back_to_stage'].includes(value)
		},
		stageData: {
			type: Object,
			required: true
		},
		formatApprovers: {
			type: Function,
			required: true
		},
		formatFollowers: {
			type: Function,
			required: true
		}
	},
	
	setup(props) {
		const { t } = useI18n();
		
		// Computed property to get the correct stage data based on stage type
		const getStageData = computed(() => {
			switch (props.stageType) {
				case 'from_stage':
					return props.stageData.from_stage;
				case 'to_stage':
					return props.stageData.to_stage;
				case 'back_to_stage':
					return props.stageData.back_to_stage;
				default:
					return null;
			}
		});
		
		return {
			getStageData
		}
	}
});
</script>

<style scoped>
/* Component-specific styles if needed */
</style>
